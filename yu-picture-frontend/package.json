{"name": "yu-picture-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "pure-build": "vite build", "build": "run-p type-check \"build-only {@}\" --", "openapi": "node openapi.config.js", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.7.9", "echarts": "^5.5.1", "echarts-wordcloud": "^2.1.0", "file-saver": "^2.0.5", "pinia": "^2.2.6", "vue": "^3.5.13", "vue-cropper": "^1.1.4", "vue-echarts": "^7.0.3", "vue-router": "^4.4.5", "vue3-colorpicker": "^2.3.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/file-saver": "^2.0.7", "@types/node": "^22.9.3", "@umijs/openapi": "^1.13.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.1", "prettier": "^3.3.3", "typescript": "~5.6.3", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.5", "vue-tsc": "^2.1.10"}}