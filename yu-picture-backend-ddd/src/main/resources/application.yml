server:
  port: 8123
  servlet:
    context-path: /api
    # cookie 30 天过期
    session:
      cookie:
        max-age: 2592000
spring:
  application:
    name: yu-picture-backend
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************
    username: root
    password: 123456
  # Redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 5000
  # Session 配置
  session:
    store-type: redis
    # session 30 天后过期
    timeout: 2592000
  # 开放更大的文件上传体积
  servlet:
    multipart:
      max-file-size: 10MB
  # 空间图片分表
  shardingsphere:
    datasource:
      names: yu_picture
      yu_picture:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: **************************************
        username: root
        password: 123456
    rules:
      sharding:
        tables:
          picture:
            actual-data-nodes: yu_picture.picture # 动态分表
            table-strategy:
              standard:
                sharding-column: spaceId
                sharding-algorithm-name: picture_sharding_algorithm  # 使用自定义分片算法
        sharding-algorithms:
          picture_sharding_algorithm:
            type: CLASS_BASED
            props:
              strategy: standard
              algorithmClassName: com.yupi.yupicture.shared.sharding.PictureShardingAlgorithm
    props:
      sql-show: true
mybatis-plus:
  configuration:
    # MyBatis 配置
    map-underscore-to-camel-case: false
    # 仅在开发环境打印日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    enable-sql-runner: true
    db-config:
      logic-delete-field: isDelete # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值（默认为 1）
      logic-not-delete-value: 0 # 逻辑未删除值（默认为 0）
# 接口文档配置
knife4j:
  enable: true
  openapi:
    title: 接口文档
    version: 1.0
    group:
      default:
        api-rule: package
        api-rule-resources:
          - com.yupi.yupicture.interfaces.controller
## 对象存储配置（需要从腾讯云获取）
#cos:
#  client:
#    host: xxx
#    secretId: xxx
#    secretKey: xxx
#    region: xxx
#    bucket: xxx

## 阿里云 AI 配置
#aliYunAi:
#  apiKey: xxx