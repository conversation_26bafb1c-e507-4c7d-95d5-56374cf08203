<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.yupicture.infrastructure.mapper.PictureMapper">

    <resultMap id="BaseResultMap" type="com.yupi.yupicture.domain.picture.entity.Picture">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="thumbnailUrl" column="thumbnailUrl" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="introduction" column="introduction" jdbcType="VARCHAR"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="tags" column="tags" jdbcType="VARCHAR"/>
            <result property="picSize" column="picSize" jdbcType="BIGINT"/>
            <result property="picWidth" column="picWidth" jdbcType="INTEGER"/>
            <result property="picHeight" column="picHeight" jdbcType="INTEGER"/>
            <result property="picScale" column="picScale" jdbcType="DOUBLE"/>
            <result property="picFormat" column="picFormat" jdbcType="VARCHAR"/>
            <result property="picColor" column="picColor" jdbcType="VARCHAR"/>
            <result property="userId" column="userId" jdbcType="BIGINT"/>
            <result property="spaceId" column="spaceId" jdbcType="BIGINT"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="editTime" column="editTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
            <result property="reviewStatus" column="reviewStatus" jdbcType="INTEGER"/>
            <result property="reviewMessage" column="reviewMessage" jdbcType="VARCHAR"/>
            <result property="reviewerId" column="reviewerId" jdbcType="BIGINT"/>
            <result property="reviewTime" column="reviewTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,url,thumbnailUrl,name,
        introduction,category,tags,
        picSize,picWidth,picHeight,
        picScale,picFormat,userId,spaceId,
        createTime,editTime,updateTime,
        isDelete,reviewStatus,reviewMessage,
        reviewerId,reviewTime
    </sql>
</mapper>
