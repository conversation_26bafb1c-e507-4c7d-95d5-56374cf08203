<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.yupicture.infrastructure.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.yupi.yupicture.domain.user.entity.User">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userAccount" column="userAccount" jdbcType="VARCHAR"/>
            <result property="userPassword" column="userPassword" jdbcType="VARCHAR"/>
            <result property="userName" column="userName" jdbcType="VARCHAR"/>
            <result property="userAvatar" column="userAvatar" jdbcType="VARCHAR"/>
            <result property="userProfile" column="userProfile" jdbcType="VARCHAR"/>
            <result property="userRole" column="userRole" jdbcType="VARCHAR"/>
            <result property="editTime" column="editTime" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,userAccount,userPassword,
        userName,userAvatar,userProfile,
        userRole,editTime,createTime,
        updateTime,isDelete
    </sql>
</mapper>
