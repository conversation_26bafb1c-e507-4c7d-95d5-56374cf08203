<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.yupicture.infrastructure.mapper.SpaceMapper">

    <resultMap id="BaseResultMap" type="com.yupi.yupicture.domain.space.entity.Space">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="spaceName" column="spaceName" jdbcType="VARCHAR"/>
            <result property="spaceLevel" column="spaceLevel" jdbcType="INTEGER"/>
            <result property="spaceType" column="spaceType" jdbcType="INTEGER"/>
            <result property="maxSize" column="maxSize" jdbcType="BIGINT"/>
            <result property="maxCount" column="maxCount" jdbcType="BIGINT"/>
            <result property="totalSize" column="totalSize" jdbcType="BIGINT"/>
            <result property="totalCount" column="totalCount" jdbcType="BIGINT"/>
            <result property="userId" column="userId" jdbcType="BIGINT"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="editTime" column="editTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,spaceName,spaceLevel,spaceType,
        maxSize,maxCount,totalSize,
        totalCount,userId,createTime,
        editTime,updateTime,isDelete
    </sql>
</mapper>
