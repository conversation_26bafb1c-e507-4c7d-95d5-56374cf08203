package com.yupi.yupicturebackend.exception;

import lombok.Getter;

@Getter
public enum ErrorCode {

    SUCCESS(0, "ok"),                          // HTTP 200
    PARAMS_ERROR(40000, "请求参数错误"),         // HTTP 400
    NOT_LOGIN_ERROR(40100, "未登录"),           // HTTP 401
    NO_AUTH_ERROR(40101, "无权限"),             // HTTP 403
    NOT_FOUND_ERROR(40400, "请求数据不存在"),    // HTTP 404
    FORBIDDEN_ERROR(40300, "禁止访问"),         // HTTP 403
    SYSTEM_ERROR(50000, "系统内部异常"),         // HTTP 500
    OPERATION_ERROR(50001, "操作失败");         // HTTP 500

    /**
     * 状态码
     */
    private final int code;

    /**
     * 信息
     */
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

}