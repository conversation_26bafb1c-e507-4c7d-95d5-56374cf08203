<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.yupicturebackend.mapper.SpaceUserMapper">

    <resultMap id="BaseResultMap" type="com.yupi.yupicturebackend.model.entity.SpaceUser">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="spaceId" column="spaceId" jdbcType="BIGINT"/>
            <result property="userId" column="userId" jdbcType="BIGINT"/>
            <result property="spaceRole" column="spaceRole" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,spaceId,userId,
        spaceRole,createTime,updateTime
    </sql>
</mapper>
