### 1.1 添加成员到私有空间-管理员(失败)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{privateSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.2 添加成员到私有空间-空间创建人(失败)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{privateSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.3 添加成员到私有空间-非空间创建人(失败)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{privateSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.4 添加成员到企业空间-管理员(成功)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{teamSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.5 添加成员到企业空间-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{teamSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.6 添加成员到企业空间-协作者(失败)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{editorCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{teamSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.7 添加成员到企业空间-只读用户(失败)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{viewerCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{teamSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 1.8 添加成员到企业空间-非空间用户(失败)
POST http://localhost:8123/api/spaceUser/add
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "userId": {{addUserId}},
  "spaceId": {{teamSpaceId}},
  "spaceRole": "{{spaceUserRole}}"
}

### 2.1 从私有空间移除成员-管理员(失败)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "id": {{privateSpaceUserId}}
}

### 2.2 从私有空间移除成员-空间创建人(失败)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "id": {{privateSpaceUserId}}
}

### 2.3 从私有空间移除成员-非空间创建人(失败)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "id": {{privateSpaceUserId}}
}

### 2.4 从企业空间移除成员-管理员(成功)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "id": {{teamSpaceUserId}}
}

### 2.5 从企业空间移除成员-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "id": {{teamSpaceUserId}}
}

### 2.6 从企业空间移除成员-协作者(失败)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{editorCookie}}

{
  "id": {{teamSpaceUserId}}
}

### 2.7 从企业空间移除成员-只读用户(失败)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{viewerCookie}}

{
  "id": {{teamSpaceUserId}}
}

### 2.8 从企业空间移除成员-非空间用户(失败)
POST http://localhost:8123/api/spaceUser/delete
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "id": {{teamSpaceUserId}}
}

### 3.1 查询私有空间成员信息-管理员(成功)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "userId": {{privateSpaceUserId}},
  "spaceId": {{privateSpaceUserSpaceId}}
}

### 3.2 查询私有空间成员信息-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "userId": {{privateSpaceUserId}},
  "spaceId": {{privateSpaceUserSpaceId}}
}

### 3.3 查询私有空间成员信息-非空间创建人(失败)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "userId": {{privateSpaceUserId}},
  "spaceId": {{privateSpaceUserSpaceId}}
}

### 3.4 查询企业空间成员信息-管理员(失败)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "userId": {{teamSpaceUserId}},
  "spaceId": {{teamSpaceId}}
}

### 3.5 查询企业空间成员信息-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "userId": {{teamSpaceUserId}},
  "spaceId": {{teamSpaceId}}
}

### 3.6 查询企业空间成员信息-协作者(失败)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{editorCookie}}

{
  "userId": {{teamSpaceUserId}},
  "spaceId": {{teamSpaceId}}
}

### 3.7 查询企业空间成员信息-只读用户(失败)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{viewerCookie}}

{
  "userId": {{teamSpaceUserId}},
  "spaceId": {{teamSpaceId}}
}

### 3.8 查询企业空间成员信息-非空间用户(失败)
POST http://localhost:8123/api/spaceUser/get
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "userId": {{teamSpaceUserId}},
  "spaceId": {{teamSpaceId}}
}

### 4.1 查询私有空间成员列表-管理员(成功)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "spaceId": {{privateSpaceId}}
}

### 4.2 查询私有空间成员列表-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "spaceId": {{privateSpaceId}}
}

### 4.3 查询私有空间成员列表-非空间创建人(失败)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "spaceId": {{privateSpaceId}}
}

### 4.4 查询企业空间成员列表-管理员(失败)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "spaceId": {{teamSpaceId}}
}

### 4.5 查询企业空间成员列表-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "spaceId": {{teamSpaceId}}
}

### 4.6 查询企业空间成员列表-协作者(失败)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{editorCookie}}

{
  "spaceId": {{teamSpaceId}}
}

### 4.7 查询企业空间成员列表-只读用户(失败)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{viewerCookie}}

{
  "spaceId": {{teamSpaceId}}
}

### 4.8 查询企业空间成员列表-非空间用户(失败)
POST http://localhost:8123/api/spaceUser/list
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "spaceId": {{teamSpaceId}}
}

### 5.1 编辑私有空间成员信息-管理员(成功)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "id": {{privateSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.2 编辑私有空间成员信息-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "id": {{privateSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.3 编辑私有空间成员信息-非空间创建人(失败)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "id": {{privateSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.4 编辑企业空间成员信息-管理员(失败)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{adminCookie}}

{
  "id": {{teamSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.5 编辑企业空间成员信息-空间创建人(成功)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{spaceCreatorCookie}}

{
  "id": {{teamSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.6 编辑企业空间成员信息-协作者(失败)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{editorCookie}}

{
  "id": {{teamSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.7 编辑企业空间成员信息-只读用户(失败)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{viewerCookie}}

{
  "id": {{teamSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}

### 5.8 编辑企业空间成员信息-非空间用户(失败)
POST http://localhost:8123/api/spaceUser/edit
Content-Type: application/json
Cookie: {{commonUserCookie}}

{
  "id": {{teamSpaceUserId}},
  "spaceRole": "{{spaceUserEditorRole}}"
}
